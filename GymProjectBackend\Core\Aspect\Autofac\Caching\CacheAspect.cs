using Castle.DynamicProxy;
using Core.CrossCuttingConcerns.Caching;
using Core.Utilities.Interceptors;
using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Diagnostics;
using System.Linq;
using System.Reflection;
using System.Text;

namespace Core.Aspects.Autofac.Caching
{
    /// <summary>
    /// AOP Cache Aspect - Method'lara otomatik cache ekleme
    /// Multi-tenant aware, parameter-based key generation
    /// Performance monitoring ve cache hit/miss logging
    /// </summary>
    public class CacheAspect : MethodInterception
    {
        private readonly int _duration;
        private readonly ICacheService _cacheService;
        private readonly ICompanyContext _companyContext;
        private readonly Stopwatch _stopwatch;

        /// <summary>
        /// Cache aspect constructor
        /// </summary>
        /// <param name="duration">Cache süresi (saniye)</param>
        public CacheAspect(int duration)
        {
            _duration = duration;
            _cacheService = ServiceTool.ServiceProvider.GetService<ICacheService>();
            _companyContext = ServiceTool.ServiceProvider.GetService<ICompanyContext>();
            _stopwatch = new Stopwatch();
        }

        public override void Intercept(IInvocation invocation)
        {
            var methodName = $"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}";
            var cacheKey = GenerateCacheKey(invocation);
            var returnType = invocation.Method.ReturnType;

            _stopwatch.Start();

            try
            {
                // Cache'den veri al - Return type'a göre generic method çağır
                var cachedValue = GetCachedValue(cacheKey, returnType);

                if (cachedValue != null)
                {
                    // Cache hit - Cached değeri döndür
                    invocation.ReturnValue = cachedValue;
                    _stopwatch.Stop();

                    LogCacheHit(methodName, cacheKey, _stopwatch.ElapsedMilliseconds);
                    return;
                }

                // Cache miss - Method'u çalıştır
                invocation.Proceed();

                // Return value'yu cache'le
                if (invocation.ReturnValue != null)
                {
                    var expiry = TimeSpan.FromSeconds(_duration);
                    SetCachedValue(cacheKey, invocation.ReturnValue, returnType, expiry);
                }

                _stopwatch.Stop();
                LogCacheMiss(methodName, cacheKey, _stopwatch.ElapsedMilliseconds);
            }
            catch (Exception ex)
            {
                _stopwatch.Stop();
                LogCacheError(methodName, cacheKey, ex);
                throw;
            }
        }

        /// <summary>
        /// Method signature ve parametrelerine göre cache key oluşturur
        /// Format: "gym:{companyId}:{className}:{methodName}:{parameterHash}"
        /// </summary>
        private string GenerateCacheKey(IInvocation invocation)
        {
            try
            {
                var companyId = _companyContext.GetCompanyId();

                // Interface adından entity adını çıkar
                // IMemberService -> imemberservice (mevcut sistem)
                // MemberManager -> member (alternatif)
                var typeName = invocation.Method.ReflectedType.Name;
                var className = typeName.Replace("Manager", "").ToLowerInvariant();

                var methodName = invocation.Method.Name.ToLowerInvariant();

                // Parameter hash oluştur
                var parameterHash = GenerateParameterHash(invocation);

                // CacheKeyHelper kullanarak hierarchical key oluştur
                // Format: gym:{companyId}:{entity}:{action}:{additionalParts}
                return CacheKeyHelper.GenerateKey(companyId, className, methodName, parameterHash);
            }
            catch (Exception ex)
            {
                // Fallback key generation
                var fallbackKey = $"gym:{_companyContext.GetCompanyId()}:cache_error:{invocation.Method.Name}:{DateTime.UtcNow.Ticks}";
                LogCacheError($"{invocation.Method.ReflectedType.FullName}.{invocation.Method.Name}",
                             fallbackKey, ex);
                return fallbackKey;
            }
        }

        /// <summary>
        /// Method parametrelerinden hash oluşturur
        /// </summary>
        private string GenerateParameterHash(IInvocation invocation)
        {
            if (invocation.Arguments == null || invocation.Arguments.Length == 0)
            {
                return "noparams";
            }

            var paramBuilder = new StringBuilder();
            var parameters = invocation.Method.GetParameters();

            for (int i = 0; i < invocation.Arguments.Length; i++)
            {
                var arg = invocation.Arguments[i];
                var paramName = parameters[i].Name;

                if (arg == null)
                {
                    paramBuilder.Append($"{paramName}:null");
                }
                else if (IsPrimitiveType(arg.GetType()))
                {
                    paramBuilder.Append($"{paramName}:{arg}");
                }
                else
                {
                    // Complex object için JSON hash
                    try
                    {
                        var json = JsonConvert.SerializeObject(arg, Formatting.None);
                        var hash = json.GetHashCode().ToString("x8");
                        paramBuilder.Append($"{paramName}:{hash}");
                    }
                    catch
                    {
                        paramBuilder.Append($"{paramName}:{arg.GetType().Name}");
                    }
                }

                if (i < invocation.Arguments.Length - 1)
                    paramBuilder.Append("_");
            }

            return paramBuilder.ToString().ToLowerInvariant();
        }

        /// <summary>
        /// Primitive type kontrolü
        /// </summary>
        private bool IsPrimitiveType(Type type)
        {
            return type.IsPrimitive || 
                   type == typeof(string) || 
                   type == typeof(DateTime) || 
                   type == typeof(decimal) || 
                   type == typeof(Guid) ||
                   type.IsEnum;
        }

        /// <summary>
        /// Cache hit logging
        /// </summary>
        private void LogCacheHit(string methodName, string cacheKey, long elapsedMs)
        {
            var methodShort = methodName.Split('.').LastOrDefault()?.Replace("Manager", "");
            Console.ForegroundColor = ConsoleColor.Green;
            Console.WriteLine($"[CACHE HIT] {methodShort} - {elapsedMs}ms");
            Console.ResetColor();
        }

        /// <summary>
        /// Cache miss logging
        /// </summary>
        private void LogCacheMiss(string methodName, string cacheKey, long elapsedMs)
        {
            var methodShort = methodName.Split('.').LastOrDefault()?.Replace("Manager", "");
            Console.ForegroundColor = ConsoleColor.Yellow;
            Console.WriteLine($"[CACHE MISS] {methodShort} - {elapsedMs}ms (cached {_duration}s)");
            Console.ResetColor();
        }

        /// <summary>
        /// Cache error logging
        /// </summary>
        private void LogCacheError(string methodName, string cacheKey, Exception ex)
        {
            var logMessage = $"CACHE ERROR: {methodName} | Key: {cacheKey} | Error: {ex.Message}";
            Console.WriteLine($"[CACHE] {logMessage}");

            // TODO: ILogService ile structured logging eklenebilir
        }

        /// <summary>
        /// Type-safe cache value retrieval
        /// </summary>
        private object GetCachedValue(string cacheKey, Type returnType)
        {
            try
            {
                Console.WriteLine($"[CACHE DEBUG] GetCachedValue - Key: {cacheKey}, Type: {returnType.FullName}");

                // Basit string get ile test edelim
                var stringValue = _cacheService.Get<string>(cacheKey);
                Console.WriteLine($"[CACHE DEBUG] String Value: {(stringValue != null ? "Found" : "Not Found")}");

                if (stringValue == null)
                    return null;

                // JSON'dan deserialize et - Interface type'lar için concrete type kullan
                var result = DeserializeWithConcreteType(stringValue, returnType);

                Console.WriteLine($"[CACHE DEBUG] GetCachedValue Result: {(result != null ? "Found" : "Not Found")}");
                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] GetCachedValue Error: {ex.Message}");
                LogCacheError("GetCachedValue", cacheKey, ex);
                return null;
            }
        }

        /// <summary>
        /// Type-safe cache value storage
        /// </summary>
        private void SetCachedValue(string cacheKey, object value, Type valueType, TimeSpan expiry)
        {
            try
            {
                Console.WriteLine($"[CACHE DEBUG] SetCachedValue - Key: {cacheKey}, Type: {valueType.FullName}, Expiry: {expiry.TotalSeconds}s");

                // JSON serialize edip string olarak kaydet - TypeNameHandling.Auto ile
                var settings = new JsonSerializerSettings
                {
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Include
                };
                var jsonValue = JsonConvert.SerializeObject(value, settings);
                _cacheService.Set(cacheKey, jsonValue, expiry);

                Console.WriteLine($"[CACHE DEBUG] SetCachedValue Success");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] SetCachedValue Error: {ex.Message}");
                LogCacheError("SetCachedValue", cacheKey, ex);
            }
        }

        /// <summary>
        /// Interface type'lar için concrete type ile deserialization
        /// </summary>
        private object DeserializeWithConcreteType(string jsonValue, Type returnType)
        {
            try
            {
                Console.WriteLine($"[CACHE DEBUG] JSON Value Length: {jsonValue?.Length ?? 0}");
                if (!string.IsNullOrEmpty(jsonValue))
                {
                    Console.WriteLine($"[CACHE DEBUG] JSON Value Preview: {jsonValue.Substring(0, Math.Min(100, jsonValue.Length))}...");
                }

                // IDataResult<T> interface'i için manuel deserialization
                if (returnType.IsGenericType && returnType.GetGenericTypeDefinition() == typeof(Core.Utilities.Results.IDataResult<>))
                {
                    var genericArg = returnType.GetGenericArguments()[0];

                    // JSON'u JObject olarak parse et
                    if (string.IsNullOrEmpty(jsonValue))
                        return null;

                    var jsonObject = Newtonsoft.Json.Linq.JObject.Parse(jsonValue);

                    // Property'leri kontrol et
                    var properties = string.Join(", ", jsonObject.Properties().Select(p => p.Name));
                    Console.WriteLine($"[CACHE DEBUG] JSON Properties: {properties}");

                    // Data, Success, Message'ı ayrı ayrı çıkar
                    var dataToken = jsonObject["Data"];
                    var successToken = jsonObject["Success"];
                    var messageToken = jsonObject["Message"];

                    Console.WriteLine($"[CACHE DEBUG] Success Token: {successToken}");
                    Console.WriteLine($"[CACHE DEBUG] Message Token: {messageToken}");
                    Console.WriteLine($"[CACHE DEBUG] Data Token Type: {dataToken?.Type}");

                    // Data'yı generic type'a deserialize et
                    var data = dataToken != null && dataToken.Type != Newtonsoft.Json.Linq.JTokenType.Null
                        ? dataToken.ToObject(genericArg)
                        : null;

                    var success = successToken?.ToObject<bool>() ?? true;
                    var message = messageToken?.ToObject<string>();

                    Console.WriteLine($"[CACHE DEBUG] Deserialized Success: {success}");
                    Console.WriteLine($"[CACHE DEBUG] Deserialized Message: {message}");
                    Console.WriteLine($"[CACHE DEBUG] Deserialized Data: {(data != null ? "Not Null" : "Null")}");

                    // SuccessDataResult'ı (data, message) constructor'ı ile oluştur
                    var concreteType = typeof(Core.Utilities.Results.SuccessDataResult<>).MakeGenericType(genericArg);

                    object? result;
                    if (!string.IsNullOrEmpty(message))
                    {
                        // (T data, string message) constructor
                        result = Activator.CreateInstance(concreteType, data, message);
                    }
                    else
                    {
                        // (T data) constructor
                        result = Activator.CreateInstance(concreteType, data);
                    }

                    Console.WriteLine($"[CACHE DEBUG] Manual Deserialization Success - Data Type: {data?.GetType().Name ?? "Null"}");
                    return result;
                }

                // Diğer interface'ler için varsayılan deserialization
                if (string.IsNullOrEmpty(jsonValue))
                    return null;

                return JsonConvert.DeserializeObject(jsonValue, returnType);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[CACHE DEBUG] DeserializeWithConcreteType Error: {ex.Message}");
                Console.WriteLine($"[CACHE DEBUG] Stack Trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}
