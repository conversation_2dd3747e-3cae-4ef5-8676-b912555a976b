﻿{
    "data":  {
                 "data":  [
                              {
                                  "memberID":  2537,
                                  "membershipID":  4369,
                                  "name":  "MURAT TEPE",
                                  "phoneNumber":  "05465959595",
                                  "gender":  1,
                                  "branch":  "Fitness",
                                  "remainingDays":  6,
                                  "isActive":  true,
                                  "startDate":  "2025-07-22T00:00:00",
                                  "endDate":  "2025-08-05T00:00:00",
                                  "updatedDate":  "2025-07-22T19:01:13.607",
                                  "isFutureStartDate":  false
                              },
                              {
                                  "memberID":  1,
                                  "membershipID":  3368,
                                  "name":  "OSMAN KESKİN",
                                  "phoneNumber":  "05393314848",
                                  "gender":  1,
                                  "branch":  "Fitness",
                                  "remainingDays":  11,
                                  "isActive":  true,
                                  "startDate":  "2025-07-11T00:00:00",
                                  "endDate":  "2025-08-10T00:00:00",
                                  "updatedDate":  "2025-07-11T03:06:13.65",
                                  "isFutureStartDate":  false
                              },
                              {
                                  "memberID":  2388,
                                  "membershipID":  3355,
                                  "name":  "SALİH BUĞRA KAYMAK",
                                  "phoneNumber":  "05448255130",
                                  "gender":  1,
                                  "branch":  "Fitness",
                                  "remainingDays":  7,
                                  "isActive":  true,
                                  "startDate":  "2025-07-07T00:00:00",
                                  "endDate":  "2025-08-06T00:00:00",
                                  "updatedDate":  "2025-07-07T17:12:03.293",
                                  "isFutureStartDate":  false
                              },
                              {
                                  "memberID":  2531,
                                  "membershipID":  3354,
                                  "name":  "ASLI SERT",
                                  "phoneNumber":  "05314732122",
                                  "gender":  2,
                                  "branch":  "Fitness",
                                  "remainingDays":  8,
                                  "isActive":  true,
                                  "startDate":  "2025-07-08T00:00:00",
                                  "endDate":  "2025-08-07T00:00:00",
                                  "updatedDate":  "2025-07-07T14:08:50.683",
                                  "isFutureStartDate":  false
                              },
                              {
                                  "memberID":  2530,
                                  "membershipID":  3353,
                                  "name":  "YASİN SERT",
                                  "phoneNumber":  "05462700620",
                                  "gender":  1,
                                  "branch":  "Fitness",
                                  "remainingDays":  8,
                                  "isActive":  true,
                                  "startDate":  "2025-07-08T00:00:00",
                                  "endDate":  "2025-08-07T00:00:00",
                                  "updatedDate":  "2025-07-07T14:08:10.52",
                                  "isFutureStartDate":  false
                              }
                          ],
                 "pageNumber":  1,
                 "pageSize":  5,
                 "totalCount":  51,
                 "totalPages":  11,
                 "hasPrevious":  false,
                 "hasNext":  true
             },
    "success":  true,
    "message":  null
}
