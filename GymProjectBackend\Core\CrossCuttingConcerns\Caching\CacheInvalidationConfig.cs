using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache invalidation kurallarını merkezi olarak yöneten configuration sınıfı
    /// Entity bazlı cache dependency mapping'i
    /// </summary>
    public static class CacheInvalidationConfig
    {
        /// <summary>
        /// Entity güncellendiğinde hangi cache pattern'lerinin temizleneceğini belirler
        /// Key: Entity adı, Value: Temizlenecek cache pattern'leri
        /// </summary>
        public static readonly Dictionary<string, string[]> EntityCacheDependencies = new()
        {
            // Member entity güncellendiğinde temizlenecek cache'ler
            ["Member"] = new[]
            {
                "imemberservice:*",
                "imemberdetailsservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Membership entity güncellendiğinde temizlenecek cache'ler
            ["Membership"] = new[]
            {
                "imembershipservice:*",
                "imemberservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*"
            },

            // Payment entity güncellendiğinde temizlenecek cache'ler
            ["Payment"] = new[]
            {
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imembershipservice:*",
                "imemberservice:*"
            },

            // Product entity güncellendiğinde temizlenecek cache'ler
            ["Product"] = new[]
            {
                "iproductservice:*",
                "itransactionservice:*",
                "ipaymentservice:*"
            },

            // WorkoutProgram entity güncellendiğinde temizlenecek cache'ler
            ["WorkoutProgram"] = new[]
            {
                "iworkoutprogramservice:*",
                "imemberworkoutprogramservice:*",
                "iworkoutprogramtemplateservice:*"
            },

            // User entity güncellendiğinde temizlenecek cache'ler
            ["User"] = new[]
            {
                "iuserservice:*",
                "imemberservice:*",
                "icompanyuserservice:*"
            },

            // CompanyAdress entity güncellendiğinde temizlenecek cache'ler
            ["CompanyAdress"] = new[]
            {
                "icompanyadressservice:*",
                "icompanyservice:*"
            },

            // CompanyExercise entity güncellendiğinde temizlenecek cache'ler
            ["CompanyExercise"] = new[]
            {
                "icompanyexerciseservice:*",
                "icombinedexerciseservice:*",
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Company entity güncellendiğinde temizlenecek cache'ler
            ["Company"] = new[]
            {
                "icompanyservice:*",
                "icompanyadressservice:*",
                "icompanyuserservice:*",
                "iusercompanyservice:*"
            },

            // CompanyUser entity güncellendiğinde temizlenecek cache'ler
            ["CompanyUser"] = new[]
            {
                "icompanyuserservice:*",
                "icompanyservice:*",
                "iuserservice:*",
                "iusercompanyservice:*"
            },

            // DebtPayment entity güncellendiğinde temizlenecek cache'ler
            ["DebtPayment"] = new[]
            {
                "idebtpaymentservice:*",
                "ipaymentservice:*",
                "iremainingdebtservice:*"
            },

            // ExerciseCategory entity güncellendiğinde temizlenecek cache'ler
            ["ExerciseCategory"] = new[]
            {
                "iexercisecategoryservice:*",
                "isystemexerciseservice:*",
                "icompanyexerciseservice:*",
                "icombinedexerciseservice:*",
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Expense entity güncellendiğinde temizlenecek cache'ler
            ["Expense"] = new[]
            {
                "iexpenseservice:*"
            },

            // LicensePackage entity güncellendiğinde temizlenecek cache'ler
            ["LicensePackage"] = new[]
            {
                "ilicensepackageservice:*",
                "iuserlicenseservice:*",
                "ilicensetransactionservice:*"
            },

            // LicenseTransaction entity güncellendiğinde temizlenecek cache'ler
            ["LicenseTransaction"] = new[]
            {
                "ilicensetransactionservice:*",
                "iuserlicenseservice:*"
            },

            // MembershipFreezeHistory entity güncellendiğinde temizlenecek cache'ler
            ["MembershipFreezeHistory"] = new[]
            {
                "imembershipfreezehistoryservice:*",
                "imembershipservice:*"
            },

            // MembershipType entity güncellendiğinde temizlenecek cache'ler
            ["MembershipType"] = new[]
            {
                "imembershiptypeservice:*",
                "imembershipservice:*"
            },

            // MemberWorkoutProgram entity güncellendiğinde temizlenecek cache'ler
            ["MemberWorkoutProgram"] = new[]
            {
                "imemberworkoutprogramservice:*"
            },

            // OperationClaim entity güncellendiğinde temizlenecek cache'ler
            ["OperationClaim"] = new[]
            {
                "ioperationclaimservice:*",
                "iuseroperationclaimservice:*"
            },

            // UserOperationClaim entity güncellendiğinde temizlenecek cache'ler
            ["UserOperationClaim"] = new[]
            {
                "iuseroperationclaimservice:*"
            },

            // WorkoutProgramTemplate entity güncellendiğinde temizlenecek cache'ler
            ["WorkoutProgramTemplate"] = new[]
            {
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // RemainingDebt entity güncellendiğinde temizlenecek cache'ler
            ["RemainingDebt"] = new[]
            {
                "iremainingdebtservice:*",
                "ipaymentservice:*",
                "idebtpaymentservice:*",
                "imemberservice:*"
            },

            // SystemExercise entity güncellendiğinde temizlenecek cache'ler
            ["SystemExercise"] = new[]
            {
                "isystemexerciseservice:*",
                "icombinedexerciseservice:*",
                "iworkoutprogramtemplateservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Transaction entity güncellendiğinde temizlenecek cache'ler
            ["Transaction"] = new[]
            {
                "itransactionservice:*",
                "imemberservice:*"
            },

            // UserCompany entity güncellendiğinde temizlenecek cache'ler
            ["UserCompany"] = new[]
            {
                "iusercompanyservice:*"
            }
        };

        /// <summary>
        /// Role bazlı cache invalidation kuralları
        /// Key: Role adı, Value: Temizlenecek cache pattern'leri
        /// </summary>
        public static readonly Dictionary<string, string[]> RoleCacheDependencies = new()
        {
            // Member rolü için cache invalidation
            ["member"] = new[]
            {
                "imemberservice:*",
                "imemberdetailsservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*"
            },

            // Admin rolü için cache invalidation (daha geniş)
            ["admin"] = new[]
            {
                "imemberservice:*",
                "imemberdetailsservice:*",
                "imembershipservice:*",
                "ipaymentservice:*",
                "itransactionservice:*",
                "iremainingdebtservice:*",
                "imemberworkoutprogramservice:*",
                "iproductservice:*",
                "iworkoutprogramservice:*",
                "icompanyservice:*"
            },

            // Owner rolü için cache invalidation (en geniş)
            ["owner"] = new[]
            {
                "*" // Tüm cache'leri temizle
            }
        };

        /// <summary>
        /// Belirli bir entity için cache pattern'lerini döndürür
        /// </summary>
        /// <param name="entityName">Entity adı</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Temizlenecek cache pattern'leri</returns>
        public static string[] GetCachePatternsForEntity(string entityName, int companyId)
        {
            if (EntityCacheDependencies.TryGetValue(entityName, out var patterns))
            {
                var result = new string[patterns.Length];
                for (int i = 0; i < patterns.Length; i++)
                {
                    result[i] = $"gym:{companyId}:{patterns[i]}";
                }
                return result;
            }

            return new string[0];
        }

        /// <summary>
        /// Belirli bir role için cache pattern'lerini döndürür
        /// </summary>
        /// <param name="role">Role adı</param>
        /// <param name="companyId">Company ID</param>
        /// <returns>Temizlenecek cache pattern'leri</returns>
        public static string[] GetCachePatternsForRole(string role, int companyId)
        {
            if (RoleCacheDependencies.TryGetValue(role.ToLower(), out var patterns))
            {
                if (patterns.Length == 1 && patterns[0] == "*")
                {
                    return new[] { $"gym:{companyId}:*" };
                }

                var result = new string[patterns.Length];
                for (int i = 0; i < patterns.Length; i++)
                {
                    result[i] = $"gym:{companyId}:{patterns[i]}";
                }
                return result;
            }

            return new string[0];
        }
    }
}
